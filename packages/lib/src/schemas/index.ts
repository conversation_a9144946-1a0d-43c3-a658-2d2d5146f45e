/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/": {
        parameters: {
            query?: never
            header?: never
            path?: never
            cookie?: never
        }
        get: {
            parameters: {
                query?: never
                header?: never
                path?: never
                cookie?: never
            }
            requestBody?: never
            responses: {
                /** @description minimal k22i server */
                200: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            message: string
                        }
                    }
                }
            }
        }
        put?: never
        post?: never
        delete?: never
        options?: never
        head?: never
        patch?: never
        trace?: never
    }
    "/health": {
        parameters: {
            query?: never
            header?: never
            path?: never
            cookie?: never
        }
        get: {
            parameters: {
                query?: never
                header?: never
                path?: never
                cookie?: never
            }
            requestBody?: never
            responses: {
                /** @description Health check result */
                200: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            /** @enum {string} */
                            server: "healthy" | "dead"
                            /** @enum {string} */
                            db: "healthy" | "dead"
                        }
                    }
                }
            }
        }
        put?: never
        post?: never
        delete?: never
        options?: never
        head?: never
        patch?: never
        trace?: never
    }
    "/background-jobs/hello": {
        parameters: {
            query?: never
            header?: never
            path?: never
            cookie?: never
        }
        get?: never
        put?: never
        post: {
            parameters: {
                query?: never
                header?: never
                path?: never
                cookie?: never
            }
            /** @description Enqueue Hello Job Request */
            requestBody: {
                content: {
                    "application/json": {
                        name: string
                        delayMs?: number
                    }
                }
            }
            responses: {
                /** @description Enqueue Hello Job Success */
                201: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            success: boolean
                            jobId: string | null
                            message: string
                        }
                    }
                }
                /** @description Enqueue Hello Job Error */
                500: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            success: boolean
                            jobId: string | null
                            message: string
                        }
                    }
                }
            }
        }
        delete?: never
        options?: never
        head?: never
        patch?: never
        trace?: never
    }
    "/protected": {
        parameters: {
            query?: never
            header?: never
            path?: never
            cookie?: never
        }
        get: {
            parameters: {
                query?: never
                header?: never
                path?: never
                cookie?: never
            }
            requestBody?: never
            responses: {
                /** @description Protected route result */
                200: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            message: string
                        }
                    }
                }
            }
        }
        put?: never
        post?: never
        delete?: never
        options?: never
        head?: never
        patch?: never
        trace?: never
    }
    "/organizations/{organizationId}/members": {
        parameters: {
            query?: never
            header?: never
            path?: never
            cookie?: never
        }
        get: {
            parameters: {
                query?: {
                    cursor?: string
                    column?: string
                    limit?: number
                }
                header?: never
                path?: never
                cookie?: never
            }
            requestBody?: never
            responses: {
                /** @description Organization members */
                200: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            data: {
                                /** Format: uuid */
                                id: string
                                createdAt: string
                                updatedAt: string
                                name: string
                                email: string
                                emailVerified: boolean
                                image: string | null
                                role: string | null
                                banned: boolean | null
                                banReason: string | null
                                banExpires: string | null
                                customerId: string | null
                                /** @description Date the user joined the organization */
                                joinedAt: string
                            }[]
                            cursor: string | null
                        }
                    }
                }
                /** @description Invalid cursor format */
                400: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            error: string | null
                            message: string
                        }
                    }
                }
                /** @description Organization not found */
                404: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            error: string | null
                            message: string
                        }
                    }
                }
            }
        }
        put?: never
        post?: never
        delete?: never
        options?: never
        head?: never
        patch?: never
        trace?: never
    }
    "/users/memberships": {
        parameters: {
            query?: never
            header?: never
            path?: never
            cookie?: never
        }
        get: {
            parameters: {
                query?: never
                header?: never
                path?: never
                cookie?: never
            }
            requestBody?: never
            responses: {
                /** @description User organizations */
                200: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            /** Format: uuid */
                            id: string
                            role: string
                            organization: {
                                /** Format: uuid */
                                id: string
                                name: string
                                slug: string | null
                                logo: string | null
                            }
                        }[]
                    }
                }
            }
        }
        put?: never
        post?: never
        delete?: never
        options?: never
        head?: never
        patch?: never
        trace?: never
    }
    "/currencies": {
        parameters: {
            query?: never
            header?: never
            path?: never
            cookie?: never
        }
        get: {
            parameters: {
                query?: never
                header?: never
                path?: never
                cookie?: never
            }
            requestBody?: never
            responses: {
                /** @description Currencies */
                200: {
                    headers: {
                        [name: string]: unknown
                    }
                    content: {
                        "application/json": {
                            /** Format: uuid */
                            id: string
                            symbol: string
                            name: string
                            code: string
                        }[]
                    }
                }
            }
        }
        put?: never
        post?: never
        delete?: never
        options?: never
        head?: never
        patch?: never
        trace?: never
    }
}
export type webhooks = Record<string, never>
export interface components {
    schemas: never
    responses: never
    parameters: never
    requestBodies: never
    headers: never
    pathItems: never
}
export type $defs = Record<string, never>
export type operations = Record<string, never>
