import { type CursorConfig, decoder, parser } from "@/lib/pagination"
import {
    type Member,
    MemberSchema,
    type User,
    UserSchema,
} from "@k22i/lib/schemas/models"
import { getTableColumns } from "drizzle-orm"
import { ZodError } from "zod"
import type { z } from "zod"
import { member, user } from "./auth.models"
import {
    OrganizationMembersColumnSchema,
    getOrganizationMembersCursorConfig,
} from "./auth.schemas"

/**
 * Creates a cursor configuration for organization members pagination
 * @param column Optional base64 encoded column parameter
 * @returns CursorConfig for organization members
 * @throws Error if the column parameter is invalid
 */
export function createOrganizationMembersCursorConfig<
    T extends z.infer<typeof UserSchema>,
>(column?: string | null): CursorConfig<T> {
    // Default cursor configuration
    const defaultConfig: CursorConfig<T> = {
        cursors: getOrganizationMembersCursorConfig.cursors.map(
            ({ key, order }) => ({
                key,
                schema: getTableColumns(user)[key],
                order,
            })
        ),
        primaryCursor: {
            ...getOrganizationMembersCursorConfig.primaryCursor,
            schema: getTableColumns(user)[
                getOrganizationMembersCursorConfig.primaryCursor.key
            ],
        },
    }

    if (!column) {
        return defaultConfig
    }

    try {
        const decodedColumn = decoder(column)
        const parsedColumn = parser(decodedColumn)

        // Validate the parsed column using Zod schema
        const validatedColumns =
            OrganizationMembersColumnSchema.parse(parsedColumn)

        // Default to ASC order if not specified
        const defaultOrder = "ASC"

        // Create cursors array from the validated columns
        const cursors = validatedColumns.map((columnItem) => {
            const { key, order } = columnItem

            // Map the key to the appropriate schema
            if (key === "member.createdAt") {
                return {
                    key: "createdAt" as keyof T,
                    schema: member.createdAt,
                    order,
                }
            } else if (key === "user.email") {
                return {
                    key: "email" as keyof T,
                    schema: getTableColumns(user).email,
                    order,
                }
            }

            // This should never happen due to Zod validation
            throw new Error(`Invalid key: ${key}`)
        })

        // Get the order for the primary cursor from the first column item or use default
        const primaryOrder =
            validatedColumns.length > 0
                ? validatedColumns[0].order
                : defaultOrder

        // Create a custom cursor configuration based on the validated column parameter
        return {
            primaryCursor: {
                key: "id" as keyof T, // Keep id as primary key
                schema: getTableColumns(user).id,
                order: primaryOrder,
            },
            cursors,
        }
    } catch (error) {
        if (error instanceof ZodError) {
            throw new Error(`Invalid column parameter format: ${error.message}`)
        }
        throw new Error(
            `Error processing column parameter: ${error instanceof Error ? error.message : String(error)}`
        )
    }
}

/**
 * Creates a validated cursor payload for organization members pagination
 * @param cursor Optional base64 encoded cursor parameter
 * @returns Validated cursor payload or null
 * @throws Error if the cursor parameter is invalid
 */
export function createValidCursorPayloadForOrganizationMembers(
    cursor?: string | null
): Pick<User, "id" | "email"> | Pick<Member, "createdAt"> | null {
    if (!cursor) {
        return null
    }

    try {
        const decodedString = decoder(cursor)
        const parsedData = parser(decodedString)

        const userValidation = UserSchema.pick({
            id: true,
            email: true,
        }).safeParse(parsedData)

        const memberValidation = MemberSchema.pick({
            createdAt: true,
        }).safeParse(parsedData)

        if (userValidation.success) {
            return userValidation.data
        }

        if (memberValidation.success) {
            return memberValidation.data
        }

        return null
    } catch (error) {
        if (error instanceof ZodError) {
            throw new Error(`Invalid cursor parameter format: ${error.message}`)
        }
        throw new Error(
            `Error processing cursor parameter: ${error instanceof Error ? error.message : String(error)}`
        )
    }
}
